import { limitConcurrency } from '@/lib/helpers';
import { Prisma, prismaClient } from '@/lib/prisma';
import { CommissionCalcContext } from '@/pages/api/data_processing/commissions/commissionCalContext';
import { COMMISSION_STATUS_EDITABLE } from '@/pages/api/data_processing/commissions/commissions.constant';
import { CommissionCalculatorService } from '@/services/commission-calculation/commission-calculator';
import { CommissionRetrieval } from '@/services/commission-calculation/commissionRetrieval';
import { DataStates, type ExtAccountInfo } from '@/types';
import type { statement_data } from '@prisma/client';
import type { JsonValue } from '@prisma/client/runtime/library';
import type { CommissionCalcDTO } from 'common/dto/data_processing/commissions';
import { TransactionStatuses } from 'common/globalTypes';
import { inject, injectable } from 'inversify';

@injectable()
export class CommissionCalcProcessor {
  @inject(CommissionCalculatorService)
  commissionCalculator!: CommissionCalculatorService;

  @inject(CommissionRetrieval)
  commissionRetrieval!: CommissionRetrieval;

  async process(taskData: CommissionCalcDTO, account: ExtAccountInfo) {
    const ctx = CommissionCalcContext.init(taskData);
    const statementData = await this.commissionRetrieval.getTaskStatements(
      taskData,
      ctx.regressionTestMode ? ctx.regressionAccount : account.account_id
    );

    return await limitConcurrency(
      async (statement: statement_data) => {
        await this.run(ctx, statement);
        await this.saveResult({ ctx, statement, account });
      },
      statementData || [],
      100
    );
  }

  async run(ctx: CommissionCalcContext, statement: statement_data) {
    return await this.commissionCalculator.calculate({
      ctx,
      statement,
      // biome-ignore lint/suspicious/noExplicitAny: placeholder for now
      agentCommissionProfile: null as any,
    });
  }

  async saveResult(data: {
    ctx: CommissionCalcContext;
    statement: statement_data;
    account: ExtAccountInfo;
  }) {
    const { ctx, statement, account } = data;
    await prismaClient.$transaction(async (tx) => {
      const payoutRate = {};
      const agentCommissionPayoutRate = {};
      const commissions = ctx.agentCommissionCalc[statement.id];
      const contacts = await tx.contacts.findMany({
        where: {
          str_id: { in: Object.keys(commissions || {}) },
          account_id: account.account_id,
        },
      });
      // Probably returned from calculation, so we don't need to refetch these data again
      const contactsMap = contacts.reduce(
        (acc, contact) => {
          if (contact.str_id) {
            acc[contact.str_id] = contact;
          }
          return acc;
        },
        {} as { [strId: string]: (typeof contacts)[0] }
      );
      await tx.accounting_transaction_details.updateMany({
        where: {
          account_id: account.account_id,
          statement_id: statement.id,
          state: DataStates.ACTIVE,
        },
        data: {
          state: DataStates.DELETED,
        },
      });
      await tx.statement_data.update({
        where: {
          account_id: account.account_id,
          id: statement.id,
          OR: COMMISSION_STATUS_EDITABLE,
        },
        data: {
          agent_commissions: commissions || Prisma.DbNull,
          agent_commissions_log:
            (ctx.agentCommissionCalcLog[statement.id] as JsonValue) ||
            Prisma.DbNull,
          // @ts-expect-error ignore for now
          accounting_transaction_details: commissions
            ? {
                create: Object.entries(commissions)
                  .filter(([k, _v]) => k !== 'total')
                  .map(([k, v]) => ({
                    account_id: account.account_id,
                    created_by: account.uid,
                    created_proxied_by: account.ouid,
                    amount: +v,
                    contact_id: contactsMap[k]?.id,
                    logs: ctx.agentCommissionCalcLog[statement.id][k],
                    status: TransactionStatuses.DRAFT,
                    type: 'payable',
                  })),
              }
            : undefined,
          agent_payout_rate: payoutRate,
          agent_commission_payout_rate: agentCommissionPayoutRate,
          agent_commissions_v2: Prisma.DbNull,
          agent_commissions_status: Prisma.DbNull,
          agent_commissions_status2: Prisma.DbNull,
        },
      });

      await tx.accounting_transaction_details.updateMany({
        where: {
          account_id: account.account_id,
          statement_data: {
            id: statement.id,
            OR: COMMISSION_STATUS_EDITABLE,
          },
          state: DataStates.ACTIVE,
        },
        data: {
          state: DataStates.DELETED,
        },
      });
    });
  }
}

// getApplicableCompProfiles need to check against compGridProducts / companyProducts
