import {
  type AncestorProfile,
  fetchProfileCompanies,
  flatCompProfileSets,
} from '@/lib/commissions';
import Formatter from '@/lib/Formatter';
import { prismaClient } from '@/lib/prisma';
import { findContactInclAncestorsCTE } from '@/lib/queries';
import type { TimerStats } from '@/lib/timerStats';
import type { LRUCacheService } from '@/services/cache/lru';
import { DataStates } from '@/types';
import { numberOrDefault } from 'common/helpers';
import { injectable } from 'inversify';

@injectable()
export class CommissionCalculationProfileService {
  getEffectiveDateRangeCondition(effectiveDate?: Date | null) {
    if (!effectiveDate) return {};

    return {
      OR: [
        {
          start_date: { lte: effectiveDate },
          end_date: { gte: effectiveDate },
        },
        {
          start_date: { lte: effectiveDate },
          end_date: null,
        },
        {
          start_date: null,
          end_date: { gte: effectiveDate },
        },
        {
          start_date: null,
          end_date: null,
        },
      ],
    };
  }

  async getCompProfiles(query: {
    contactIds: number[];
    effectiveDate?: Date | null;
  }) {
    const { contactIds, effectiveDate } = query;

    if (contactIds.length === 0) {
      return { compProfileSets: [], profiles: [] };
    }
    const [profileSets, profiles] = await Promise.all([
      prismaClient.contacts_agent_commission_schedule_profiles_sets.findMany({
        where: {
          state: DataStates.ACTIVE,
          agent_commission_schedule_profiles_sets: { state: DataStates.ACTIVE },
          contact: { state: DataStates.ACTIVE, id: { in: contactIds } },
          ...(effectiveDate
            ? this.getEffectiveDateRangeCondition(effectiveDate)
            : {}),
        },
        include: {
          agent_commission_schedule_profiles_sets: {
            where: { state: DataStates.ACTIVE },
            include: {
              commission_profiles: {
                where: { state: DataStates.ACTIVE },
              },
            },
          },
        },
      }),

      prismaClient.contacts_agent_commission_schedule_profiles.findMany({
        where: {
          state: DataStates.ACTIVE,
          agent_commission_schedule_profile: { state: DataStates.ACTIVE },
          contact: { state: DataStates.ACTIVE, id: { in: contactIds } },
        },
        include: {
          agent_commission_schedule_profile: {
            where: { state: DataStates.ACTIVE },
          },
          contact: { where: { state: DataStates.ACTIVE } },
        },
      }),
    ]);

    return { compProfileSets: profileSets, profiles };
  }

  async getContactAncestorsWithCommissionProfiles(query: {
    strId?: string | null;
    effectiveDate: Date | null;
    req: { account_id: string };
    commissionCalcProfileService: CommissionCalculationProfileService;
    cache?: LRUCacheService;
    timerStats?: TimerStats;
  }): Promise<AncestorProfile[] | null> {
    const {
      strId,
      effectiveDate,
      req,
      commissionCalcProfileService,
      cache: _cache,
      timerStats,
    } = query;
    if (!strId) {
      return [];
    }
    const start = timerStats?.start();
    const contact = await findContactInclAncestorsCTE(
      strId,
      effectiveDate,
      timerStats
    );

    if (!contact) return null;
    const ancestorsFlat: AncestorProfile[] = [];
    const currentContact = contact;
    ancestorsFlat.push({
      agentName: Formatter.contact(currentContact, {
        account_id: req.account_id,
      }),
      agentId: currentContact.id,
      agentStrId: currentContact.str_id || '',
      uplineIds: currentContact.parent_relationships
        .map((pRel) => pRel.parent?.id)
        .filter((r): r is number => !!r),
      commissionProfiles: [],
    });
    let currentContacts = [{ parent: currentContact }];
    while (currentContacts.length > 0) {
      // @ts-expect-error
      const allParents = [];
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      currentContacts.forEach((c) => {
        const parentsRelationships = c.parent?.parent_relationships?.filter(
          (parentRelationship) => parentRelationship.parent
        );
        // Reduce over the array of parents to get the total of parents with a specified split
        const specifiedSplits = parentsRelationships.map(
          (pRel): number => numberOrDefault(pRel.split_percentage, 0) ?? 0
        );
        const specifiedSplitsTotal = specifiedSplits.reduce(
          (acc: number, cur: number) => acc + cur,
          0
        );
        const defaultSplit =
          (100 - specifiedSplitsTotal) / parentsRelationships.length;
        if (!parentsRelationships || parentsRelationships?.length === 0) return;
        allParents.push(...parentsRelationships);
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        parentsRelationships.forEach((pRel) => {
          if (!pRel.parent) return;
          const parent = pRel.parent;
          if (ancestorsFlat.find((a) => a.agentId === parent.id)) return;
          ancestorsFlat.push({
            agentName: Formatter.contact(parent, {
              account_id: req.account_id,
            }),
            agentId: parent.id,
            agentStrId: parent.str_id || '',
            hierarchySplitPercentage:
              +(pRel.split_percentage ?? 0) || defaultSplit,
            uplineIds: parent.parent_relationships
              ?.map((pRel) => pRel.parent?.id)
              .filter(Boolean) as number[],
            commissionProfiles: [],
          });
        });
      });
      // @ts-expect-error
      currentContacts = allParents;
    }

    const { compProfileSets, profiles: compProfiles } =
      ancestorsFlat?.length > 0
        ? await commissionCalcProfileService.getCompProfiles({
            contactIds: ancestorsFlat.map((a) => a.agentId),
            effectiveDate,
          })
        : { compProfileSets: [], profiles: [] };
    for (const ancestor of ancestorsFlat) {
      const _compProfileSets = compProfileSets.filter(
        (cps) =>
          cps.contact_id === ancestor.agentId ||
          (ancestor?.uplineIds?.includes(cps.contact_id) &&
            cps.hierarchy_processing === 'downlines')
      );

      const uniqueCompProfileSetsByProfileId =
        flatCompProfileSets(_compProfileSets);

      // @ts-expect-error
      ancestor.commissionProfiles = [
        ...compProfiles.filter(
          (cp) =>
            // @ts-expect-error
            cp.contact.str_id === ancestor.agentStrId ||
            // @ts-expect-error
            (ancestor.uplineIds.includes(cp.contact.id) &&
              cp.hierarchy_processing === 'downlines')
        ),
        ...uniqueCompProfileSetsByProfileId,
      ];

      // Fetch associated companies for each profile
      await fetchProfileCompanies(ancestor?.commissionProfiles);
    }
    const duration = timerStats?.end(
      'getContactAncestorsWithCommissionProfiles',
      // @ts-expect-error
      start
    );
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.info(
      `getContactAncestorsWithCommissionProfiles took ${duration?.toFixed(2)}s (total: ${timerStats?.get('getContactAncestorsWithCommissionProfiles')?.toFixed(2)}s)\n` +
        timerStats?.logString('getContactAncestorsWithCommissionProfiles')
    );

    return ancestorsFlat;
  }
}
