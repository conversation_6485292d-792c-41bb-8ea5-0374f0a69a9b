import type { AncestorApplicableProfile } from '@/lib/commissions';
import type { CommissionCalcContext } from '@/pages/api/data_processing/commissions/commissionCalContext';
import type { statement_data } from '@prisma/client';
import type { CALC_METHODS } from 'common/constants';
import BigNumber from 'bignumber.js';
import { inject, injectable } from 'inversify';
import { CommissionUtils } from '@/pages/api/data_processing/commissions/commissionUtils';

interface CommissionCalculationResult {
  commissionRate: BigNumber;
  commissionBasis: BigNumber;
  multiplier: BigNumber;
  splitPercentage?: BigNumber;
}

interface CommissionCalculationStrategy {
  calculate(options: {
    ctx: CommissionCalcContext;
    agentCommissionProfile: AncestorApplicableProfile;
    statement: statement_data;
  }): Promise<CommissionCalculationResult>;
}

@injectable()
export class CommissionCalculatorService {
  @inject(CommissionUtils)
  commissionUtils!: CommissionUtils;

  private strategies?: Map<
    keyof typeof CALC_METHODS,
    CommissionCalculationStrategy
  >;

  constructor() {
    this.initializeStrategies();
  }

  private initializeStrategies() {
    this.strategies = new Map<
      keyof typeof CALC_METHODS,
      CommissionCalculationStrategy
    >([
      [
        'carrierGridSplitRemainder',
        { calculate: this.carrierGridSplitRemainder.bind(this) },
      ],
      ['compGrid', { calculate: this.compGrid.bind(this) }],
      ['compGridLevel', { calculate: this.compGridLevel.bind(this) }],
      ['keepRate', { calculate: this.keepRate.bind(this) }],
      ['overrideSplit', { calculate: this.overrideSplit.bind(this) }],
      ['payHouseRate', { calculate: this.payHouseRate.bind(this) }],
      ['payoutRate', { calculate: this.payoutRate.bind(this) }],
      [
        'payoutRateIncentiveTiers',
        { calculate: this.payoutRateIncentiveTiers.bind(this) },
      ],
      [
        'payOverrideUpToTotalRate',
        { calculate: this.payOverrideUpToTotalRate.bind(this) },
      ],
      ['referral', { calculate: this.referral.bind(this) }],
    ]);
  }

  async calculate({
    ctx,
    agentCommissionProfile,
    statement,
  }: {
    ctx: CommissionCalcContext;
    agentCommissionProfile: AncestorApplicableProfile;
    statement: statement_data;
  }) {
    const calcMethod =
      agentCommissionProfile?.agentCommissionProfile?.rule?.calculation_method;
    if (!calcMethod) {
      throw new Error(
        'Calculation method is not defined in the commission rule'
      );
    }

    // Get the appropriate strategy based on the calculation method
    const strategy = this.strategies?.get(
      calcMethod as keyof typeof CALC_METHODS
    );

    if (!strategy) {
      throw new Error(`Unsupported calculation method: ${calcMethod}`);
    }

    const result = await strategy.calculate({
      ctx,
      agentCommissionProfile,
      statement,
    });
    // call adjustRate - [ set / add] to adjust the final rate if needed
    // call sequenceRunMethods [bonus / fixed] if there are any custom methods are defined
    // {basis * rate * multiplier * splitPercentage } do the final calculation using all the factors

    const profileMultiplier = BigNumber(
      agentCommissionProfile?.agentCommissionProfile?.multiplier ?? 100
    ).div(100);
    const commissionAmount = result.commissionBasis
      .multipliedBy(result.commissionRate)
      .multipliedBy(result.multiplier)
      .multipliedBy(profileMultiplier)
      .multipliedBy(BigNumber(result.splitPercentage ?? 100).div(100));
    return this.commissionUtils.alignSign(
      commissionAmount,
      this.commissionUtils.getValueSign(+result.commissionBasis),
      this.commissionUtils.getValueSign(+result.commissionRate),
      this.commissionUtils.getValueSign(+result.multiplier),
      this.commissionUtils.getValueSign(+(result.splitPercentage ?? 0)),
      this.commissionUtils.getValueSign(+(profileMultiplier ?? 0))
    );
  }

  async carrierGridSplitRemainder({
    ctx,
    agentCommissionProfile,
    statement,
  }: {
    ctx: CommissionCalcContext;
    agentCommissionProfile: AncestorApplicableProfile;
    statement: statement_data;
  }): Promise<CommissionCalculationResult> {
    // TODO: Implementation
    // call ctx.add
    return {
      commissionRate: new BigNumber(0),
      commissionBasis: new BigNumber(0),
      multiplier: new BigNumber(1),
      splitPercentage: new BigNumber(100),
    };
  }

  async compGrid({
    ctx,
    agentCommissionProfile,
    statement,
  }: {
    ctx: CommissionCalcContext;
    agentCommissionProfile: AncestorApplicableProfile;
    statement: statement_data;
  }): Promise<CommissionCalculationResult> {
    // TODO: Implementation
    return {
      commissionRate: new BigNumber(0),
      commissionBasis: new BigNumber(0),
      multiplier: new BigNumber(1),
      splitPercentage: new BigNumber(100),
    };
  }

  async compGridLevel({
    ctx,
    agentCommissionProfile,
    statement,
  }: {
    ctx: CommissionCalcContext;
    agentCommissionProfile: AncestorApplicableProfile;
    statement: statement_data;
  }): Promise<CommissionCalculationResult> {
    // TODO: Implementation
    return {
      commissionRate: new BigNumber(0),
      commissionBasis: new BigNumber(0),
      multiplier: new BigNumber(1),
      splitPercentage: new BigNumber(100),
    };
  }

  async keepRate({
    ctx,
    agentCommissionProfile,
    statement,
  }: {
    ctx: CommissionCalcContext;
    agentCommissionProfile: AncestorApplicableProfile;
    statement: statement_data;
  }): Promise<CommissionCalculationResult> {
    // TODO: Implementation
    return {
      commissionRate: new BigNumber(0),
      commissionBasis: new BigNumber(0),
      multiplier: new BigNumber(1),
      splitPercentage: new BigNumber(100),
    };
  }

  async overrideSplit({
    ctx,
    agentCommissionProfile,
    statement,
  }: {
    ctx: CommissionCalcContext;
    agentCommissionProfile: AncestorApplicableProfile;
    statement: statement_data;
  }): Promise<CommissionCalculationResult> {
    // TODO: Implementation
    return {
      commissionRate: new BigNumber(0),
      commissionBasis: new BigNumber(0),
      multiplier: new BigNumber(1),
      splitPercentage: new BigNumber(100),
    };
  }

  async payHouseRate({
    ctx,
    agentCommissionProfile,
    statement,
  }: {
    ctx: CommissionCalcContext;
    agentCommissionProfile: AncestorApplicableProfile;
    statement: statement_data;
  }): Promise<CommissionCalculationResult> {
    // TODO: Implementation
    return {
      commissionRate: new BigNumber(0),
      commissionBasis: new BigNumber(0),
      multiplier: new BigNumber(1),
      splitPercentage: new BigNumber(100),
    };
  }

  async payoutRate({
    ctx,
    agentCommissionProfile,
    statement,
  }: {
    ctx: CommissionCalcContext;
    agentCommissionProfile: AncestorApplicableProfile;
    statement: statement_data;
  }): Promise<CommissionCalculationResult> {
    // TODO: Implementation
    return {
      commissionRate: new BigNumber(0),
      commissionBasis: new BigNumber(0),
      multiplier: new BigNumber(1),
      splitPercentage: new BigNumber(100),
    };
  }

  async payoutRateIncentiveTiers({
    ctx,
    agentCommissionProfile,
    statement,
  }: {
    ctx: CommissionCalcContext;
    agentCommissionProfile: AncestorApplicableProfile;
    statement: statement_data;
  }): Promise<CommissionCalculationResult> {
    // TODO: Implementation
    return {
      commissionRate: new BigNumber(0),
      commissionBasis: new BigNumber(0),
      multiplier: new BigNumber(1),
      splitPercentage: new BigNumber(100),
    };
  }

  async payOverrideUpToTotalRate({
    ctx,
    agentCommissionProfile,
    statement,
  }: {
    ctx: CommissionCalcContext;
    agentCommissionProfile: AncestorApplicableProfile;
    statement: statement_data;
  }): Promise<CommissionCalculationResult> {
    // TODO: Implementation
    return {
      commissionRate: new BigNumber(0),
      commissionBasis: new BigNumber(0),
      multiplier: new BigNumber(1),
      splitPercentage: new BigNumber(100),
    };
  }

  async referral({
    ctx,
    agentCommissionProfile,
    statement,
  }: {
    ctx: CommissionCalcContext;
    agentCommissionProfile: AncestorApplicableProfile;
    statement: statement_data;
  }): Promise<CommissionCalculationResult> {
    // TODO: Implementation
    return {
      commissionRate: new BigNumber(0),
      commissionBasis: new BigNumber(0),
      multiplier: new BigNumber(1),
      splitPercentage: new BigNumber(100),
    };
  }
}
